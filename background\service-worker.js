class LinkedInAutomationBackground {
    constructor() {
        this.activeCampaigns = new Map();
        this.init();
    }

    init() {
        chrome.runtime.onInstalled.addListener(() => {
            this.onInstalled();
        });
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
            return true;
        });
        setTimeout(() => {
            this.loadCampaigns();
        }, 100);
    }

    onInstalled() {}

    handleMessage(message, sender, sendResponse) {
        const actions = {
            'startCampaign': () => this.startCampaign(message.campaignId, sendResponse),
            'pauseCampaign': () => this.pauseCampaign(message.campaignId, sendResponse),
            'resumeCampaign': () => this.resumeCampaign(message.campaignId, sendResponse),
            'deleteCampaign': () => this.deleteCampaign(message.campaignId, sendResponse),
            'getCampaignStatus': () => this.getCampaignStatus(message.campaignId, sendResponse),
            'openPopup': () => this.openExtensionPopup(sendResponse),
            'addProfilesRealTime': () => this.handleProfilesRealTime(message.profiles, sender, sendResponse),
            'collectionStatus': () => this.handleCollectionStatus(message.message, sender, sendResponse)
        };

        if (actions[message.action]) {
            actions[message.action]();
        } else {
            sendResponse({ error: 'Unknown action' });
        }
    }
    
    async startCampaign(campaignId, sendResponse) {
        try {
            const campaigns = await this.getCampaigns();
            if (!campaigns || !Array.isArray(campaigns)) {
                sendResponse({ error: 'Failed to load campaigns' });
                return;
            }

            const campaign = campaigns.find(c => c && c.id === campaignId);
            if (!campaign) {
                sendResponse({ error: 'Campaign not found' });
                return;
            }

            const { todayCount, dailyLimit } = await this.getSettings();
            if (todayCount >= dailyLimit) {
                sendResponse({ error: 'Daily limit reached' });
                return;
            }

            const linkedInTabs = await this.findLinkedInTabs();
            const targetUrl = campaign.targetUrl || 'https://www.linkedin.com/search/results/people/';

            if (linkedInTabs.length === 0) {
                chrome.tabs.create({ url: targetUrl }, (tab) => {
                    this.activeCampaigns.set(campaignId, { ...campaign, tabId: tab.id, status: 'running' });
                    chrome.tabs.onUpdated.addListener((tabId, changeInfo) => {
                        if (tabId === tab.id && changeInfo.status === 'complete') {
                            this.executeAutomation(tab.id, campaign);
                        }
                    });
                });
            } else {
                const tab = linkedInTabs[0];
                chrome.tabs.update(tab.id, { url: targetUrl }, () => {
                    this.activeCampaigns.set(campaignId, { ...campaign, tabId: tab.id, status: 'running' });
                    setTimeout(() => this.executeAutomation(tab.id, campaign), 2000);
                });
            }
            sendResponse({ success: true });
        } catch (error) {
            sendResponse({ error: error.message });
        }
    }
    
    async executeAutomation(tabId, campaign) {
        try {
            const targetUrl = campaign.targetUrl || campaign.targetData?.targetUrl;
            if (targetUrl) {
                chrome.tabs.update(tabId, { url: targetUrl }, () => {
                    setTimeout(() => {
                        this.sendAutomationMessage(tabId, 'startAutomation', campaign);
                    }, 3000);
                });
            } else {
                this.sendAutomationMessage(tabId, 'startAutomation', campaign);
            }
        } catch (error) {
            console.error('Error executing automation:', error);
        }
    }

    sendAutomationMessage(tabId, action, campaign) {
        chrome.tabs.sendMessage(tabId, { action, campaign }, (response) => {
            if (chrome.runtime.lastError) {
                console.error('Error communicating with content script:', chrome.runtime.lastError);
            }
        });
    }
    
    pauseCampaign(campaignId, sendResponse) {
        const campaign = this.activeCampaigns.get(campaignId);
        if (campaign) {
            campaign.status = 'paused';
            chrome.tabs.sendMessage(campaign.tabId, { action: 'stopAutomation' });
            sendResponse({ success: true });
        } else {
            sendResponse({ error: 'Campaign not found' });
        }
    }

    resumeCampaign(campaignId, sendResponse) {
        const campaign = this.activeCampaigns.get(campaignId);
        if (campaign) {
            campaign.status = 'running';
            this.executeAutomation(campaign.tabId, campaign);
            sendResponse({ success: true });
        } else {
            sendResponse({ error: 'Campaign not found' });
        }
    }

    deleteCampaign(campaignId, sendResponse) {
        const campaign = this.activeCampaigns.get(campaignId);
        if (campaign) {
            chrome.tabs.sendMessage(campaign.tabId, { action: 'stopAutomation' });
            this.activeCampaigns.delete(campaignId);
        }
        sendResponse({ success: true });
    }

    getCampaignStatus(campaignId, sendResponse) {
        sendResponse({ campaign: this.activeCampaigns.get(campaignId) || null });
    }
    
    async findLinkedInTabs() {
        return new Promise((resolve) => {
            chrome.tabs.query({ url: 'https://www.linkedin.com/*' }, resolve);
        });
    }

    async getCampaigns() {
        return [];
    }

    async getSettings() {
        return { todayCount: 0, dailyLimit: 50, actionDelay: 30 };
    }

    loadCampaigns() {}

    openExtensionPopup(sendResponse) {
        try {
            chrome.action.openPopup();
            sendResponse({ success: true });
        } catch (error) {
            sendResponse({ error: error.message });
        }
    }

    handleProfilesRealTime(profiles, sender, sendResponse) {
        try {
            chrome.runtime.sendMessage({
                action: 'profilesCollected',
                profiles: profiles,
                source: 'realtime'
            }).catch(() => {});
            sendResponse({ success: true });
        } catch (error) {
            sendResponse({ error: error.message });
        }
    }

    handleCollectionStatus(message, sender, sendResponse) {
        try {
            chrome.runtime.sendMessage({
                action: 'collectionStatusUpdate',
                message: message,
                source: 'content'
            }).catch(() => {});
            sendResponse({ success: true });
        } catch (error) {
            sendResponse({ error: error.message });
        }
    }
}

new LinkedInAutomationBackground();
