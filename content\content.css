#linkedin-automation-btn {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 9999;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.automation-panel {
    background-color: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 12px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    width: 200px;
}

.automation-panel button {
    background-color: #0077b5;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
}

.automation-panel button:hover {
    background-color: #0073b1;
}

.automation-panel button:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

.automation-status {
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 4px;
    background-color: #f3f6f8;
    text-align: center;
    margin-top: 4px;
}

.automation-status.running,
.automation-status.success {
    background-color: #0a8a0a;
    color: white;
}

.automation-status.warning {
    background-color: #f5a623;
    color: white;
}

.automation-status.error {
    background-color: #e74c3c;
    color: white;
}
